import os
import pandas as pd
import openpyxl
import msoffcrypto
import io
from tqdm import tqdm

# === 全局配置（请根据需要修改） ===
REGISTERED_DIR = "已登记"                   # 已登记目录
REGISTERED_FILE_PASSWORD = "123456abc"         # 登记文件密码
INPUT_FILE = "zhongdeng.xlsx"                        # 主文件名
SHEET_NAME = "放款主题明细"                  # Sheet 名称
OUTPUT_FILE = "ready_data.xlsx"                # 输出文件名

# === 加载主文件 ===
print(f"读取主文件：{INPUT_FILE}")
df_main = pd.read_excel(INPUT_FILE, sheet_name=SHEET_NAME, engine="openpyxl")

# === 确保目标列存在 ===
if '登记编号' not in df_main.columns:
    df_main['登记编号'] = ''
if '修改码' not in df_main.columns:
    df_main['修改码'] = ''

# === 预处理登记数据（缓存到内存） ===
contract_dict = {}  # 合同编号 → (登记编号, 修改码)
vin_dict = {}       # 车架号 → (登记编号, 修改码)

print("正在加载已登记目录下所有 Excel 文件...")

def read_protected_excel(file_path, password):
    decrypted = io.BytesIO()
    with open(file_path, "rb") as f:
        office_file = msoffcrypto.OfficeFile(f)
        office_file.load_key(password=password)
        office_file.decrypt(decrypted)
    return pd.read_excel(decrypted, engine="openpyxl")

def clean_text(val):
    return str(val).strip().replace("’", "").replace("‘", "").replace("`", "").replace(" ", "")

# 加载所有登记文件并构建索引
for file in tqdm(os.listdir(REGISTERED_DIR), desc="加载登记文件"):
    if not file.endswith(".xlsx"):
        continue
    try:
        path = os.path.join(REGISTERED_DIR, file)
        df = read_protected_excel(path, REGISTERED_FILE_PASSWORD)

        for _, row in df.iterrows():
            if len(row) > 21:
                contract_no = str(row.iloc[5]).strip()   # F列
                vin = str(row.iloc[10]).strip()          # K列
                reg_no = str(row.iloc[20]).strip()       # U列
                mod_code = str(row.iloc[21]).strip()     # V列
                if contract_no:
                    contract_dict[contract_no] = (reg_no, mod_code)
                if vin:
                    vin_dict[vin] = (reg_no, mod_code)
    except Exception as e:
        print(f"❌ 文件加载失败：{file}，错误：{e}")

# === 开始处理主表数据 ===
print("正在处理主文件数据...")

for idx in tqdm(df_main.index, desc="匹配中"):
    try:
        row = df_main.loc[idx]
        if str(row['申请方种类']).strip() != '自然人':
            continue

        is_registered = str(row['是否中重卡']).strip()
        matched = False

        if is_registered == '否':
            contract_no = str(row['合同编号']).strip()
            if contract_no in contract_dict:
                df_main.at[idx, '登记编号'] = clean_text(contract_dict[contract_no][0])
                df_main.at[idx, '修改码'] = clean_text(contract_dict[contract_no][1])
                matched = True
        else:
            vin = str(row['车架号']).strip()
            if vin in vin_dict:
                df_main.at[idx, '登记编号'] = clean_text(vin_dict[vin][0])
                df_main.at[idx, '修改码'] = clean_text(vin_dict[vin][1])
                matched = True

        if not matched:
            df_main.at[idx, '登记编号'] = "未找到"
            df_main.at[idx, '修改码'] = "未找到"

    except Exception as e:
        print(f"❌ 第 {idx + 2} 行处理出错：{e}")

# === 保存结果 ===
print(f"保存结果到：{OUTPUT_FILE}")
df_main.to_excel(OUTPUT_FILE, sheet_name=SHEET_NAME, index=False)
print("✅ 处理完成。")
